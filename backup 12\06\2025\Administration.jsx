import React, { useState, useEffect, useCallback, memo } from 'react';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import { Tabs, TabList, Tab, TabPanel } from '../../components/ui/Tabs';
import { useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import {
  FaCog,
  FaInfoCircle,
  FaBuilding,
  FaUserMd,
  FaUsers
} from 'react-icons/fa';

// Importar para asegurar que ReactDOM está disponible para modales
import '../../fixes/load-react-dom';

// Importación de componentes para pestañas
import InstitutionsTab from './administration/InstitutionsTab';
import PsychologistsTab from './administration/PsychologistsTab';
import PatientsTab from './administration/PatientsTab';

// Estilos específicos para la página de administración
import './administration/admin-styles.css';

// Componentes de InfoPanel y AccessDeniedMessage eliminados

/**
 * Componente principal del panel de administración
 */
const Administration = () => {
  // Estados
  const [activeTab, setActiveTab] = useState(0);
  const [showInfo, setShowInfo] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Obtener usuario del estado global
  const user = useSelector(state => state.auth.user);
  
  // Temporalmente mantenemos el valor hardcodeado hasta la implementación con Supabase
  const isAdmin = true; // Forzar a true para desarrollo
  
  // Handlers con useCallback para evitar re-renders innecesarios
  const handleTabChange = useCallback((index) => {
    setActiveTab(index);
  }, []);
  
  const toggleInfo = useCallback(() => {
    setShowInfo(prev => !prev);
  }, []);

  // Efecto para inicializar el contenedor de modales
  useEffect(() => {
    // Importar las utilidades de modal y aplicar correcciones
    const applyModalFixes = async () => {
      try {
        // Importar dinámicamente el módulo de utilidades de modal
        const modalUtils = await import('../../utils/modalUtils');
        
        // Aplicar correcciones completas de modales
        const result = modalUtils.fixModalIssues();
        
        if (result.success) {
          console.log('Correcciones de modales aplicadas con éxito:', result.details);
        } else {
          console.warn('Problemas al aplicar correcciones de modales:', result.message);
          
          // Si fallan las correcciones normales, cargar el script de emergencia
          const script = document.createElement('script');
          script.src = '/fix-modals.js';
          script.async = true;
          document.body.appendChild(script);
        }
      } catch (error) {
        console.error('Error al aplicar correcciones de modales:', error);
        
        // Solución básica como último recurso
        let modalRoot = document.getElementById('modal-root');
        if (!modalRoot) {
          console.log('Creando contenedor modal-root (método básico)');
          modalRoot = document.createElement('div');
          modalRoot.id = 'modal-root';
          document.body.appendChild(modalRoot);
        }
        
        // Configurar y limpiar
        modalRoot.style.position = 'relative';
        modalRoot.style.zIndex = '9999';
        modalRoot.innerHTML = '';
      }
    };
    
    // Ejecutar correcciones de modales
    applyModalFixes();
    
    // Forzar actualización de la UI después de un breve retraso
    const updateTimeout = setTimeout(() => {
      try {
        window.dispatchEvent(new Event('resize'));
      } catch (e) {
        console.warn('Error al forzar actualización de UI:', e);
      }
    }, 500);
    
    return () => {
      // Limpiar al desmontar
      clearTimeout(updateTimeout);
      
      try {
        const modalRoot = document.getElementById('modal-root');
        if (modalRoot) {
          modalRoot.innerHTML = '';
        }
        
        // Eliminar modales atascados
        const stuckModals = document.querySelectorAll('.fixed.inset-0');
        stuckModals.forEach(modal => {
          try {
            modal.remove();
          } catch (e) {
            console.warn('Error al eliminar modal atascado:', e);
          }
        });
      } catch (e) {
        console.error('Error durante la limpieza al desmontar:', e);
      }
    };
  }, []);

  // Efecto adicional para la carga inicial y configuración del título
  useEffect(() => {
    // Configurar título de la página
    document.title = 'Panel de Administración';
    
    // Establecer tiempo de carga para mostrar la interfaz
    const loadingTimeout = setTimeout(() => {
      setIsLoading(false);
      toast.success('Panel de Administración cargado correctamente');
    }, 500);
    
    return () => {
      clearTimeout(loadingTimeout);
    };
  }, []);

  // Renderizado condicional mientras carga
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <FaCog className="animate-spin text-blue-600 mx-auto mb-4" size={40} />
          <p className="text-gray-600">Cargando Panel de Administración...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8 administration-container">
      {/* Mostrar mensajes de error si existen */}
      {error && (
        <div className="mb-4 bg-red-50 border-l-4 border-red-500 p-4 rounded" role="alert">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Cabecera del panel */}
      <header className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 bg-gradient-to-r from-blue-600 to-indigo-950 rounded-lg shadow-lg p-6 text-white">
          <div>
            <h1 className="text-3xl font-bold mb-2 flex items-center">
              <FaCog className="mr-3 text-yellow-300" />
              Panel de Administración
            </h1>
            <p className="text-blue-100 text-lg">
              Gestión centralizada de recursos de la plataforma
            </p>
          </div>
          <div className="flex items-center mt-4 sm:mt-0">
            <button
              onClick={toggleInfo}
              className="mr-3 text-white hover:text-yellow-300 transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-500 bg-blue-700 hover:bg-blue-800 p-2 rounded-full"
              title="Mostrar información"
              aria-label="Mostrar información"
            >
              <FaInfoCircle size={20} />
            </button>
          </div>
        </div>

        {/* Panel de información colapsable eliminado */}
      </header>

      {/* Panel principal con pestañas */}
      <Card className="shadow-lg border-gray-200 rounded-xl overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-5 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-800 flex items-center">
              <FaCog className="mr-3 text-blue-600" />
              Administración de Recursos
            </h2>
            <div className="flex items-center space-x-2">
              {isAdmin ? (
                <span className="bg-green-100 text-green-800 text-xs px-3 py-1.5 rounded-full font-medium flex items-center">
                  <svg className="w-3 h-3 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Acceso Completo
                </span>
              ) : (
                <span className="bg-yellow-100 text-yellow-800 text-xs px-3 py-1.5 rounded-full font-medium flex items-center">
                  <svg className="w-3 h-3 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  Acceso Limitado
                </span>
              )}
            </div>
          </div>
        </CardHeader>
        <CardBody className="p-0">
          <Tabs activeTab={activeTab} onChange={handleTabChange}>
            <TabList className="px-6 pt-4 bg-white border-b border-gray-200">
              <Tab>
                <div className="flex items-center">
                  <FaBuilding className="mr-2 text-blue-600" />
                  <span className="font-medium">Instituciones</span>
                </div>
              </Tab>
              <Tab>
                <div className="flex items-center">
                  <FaUserMd className="mr-2 text-blue-600" />
                  <span className="font-medium">Psicólogos</span>
                </div>
              </Tab>
              <Tab>
                <div className="flex items-center">
                  <FaUsers className="mr-2 text-blue-600" />
                  <span className="font-medium">Pacientes</span>
                </div>
              </Tab>
            </TabList>

            <TabPanel>
              <div className="p-6 tab-container">
                {isAdmin ? (
                  <InstitutionsTab isAdmin={isAdmin} />
                ) : (
                  <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-yellow-700">
                          No tienes permisos para ver esta sección
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </TabPanel>
            <TabPanel>
              <div className="p-6 tab-container">
                {isAdmin ? (
                  <PsychologistsTab isAdmin={isAdmin} />
                ) : (
                  <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-yellow-700">
                          No tienes permisos para ver esta sección
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </TabPanel>
            <TabPanel>
              <div className="p-6 tab-container">
                {isAdmin ? (
                  <PatientsTab isAdmin={isAdmin} />
                ) : (
                  <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-yellow-700">
                          No tienes permisos para ver esta sección
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </TabPanel>
          </Tabs>
        </CardBody>
      </Card>
    </div>
  );
};

export default Administration;
import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';

const TestNavigation = () => {
  const { patientId } = useParams();
  const navigate = useNavigate();

  console.log('TestNavigation - patientId recibido:', patientId);

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader className="bg-green-50 border-b">
          <h2 className="text-xl font-semibold text-green-800">
            <i className="fas fa-check-circle mr-2"></i>
            Navegación Exitosa
          </h2>
        </CardHeader>
        <CardBody>
          <div className="text-center py-8">
            <div className="text-6xl text-green-500 mb-4">
              <i className="fas fa-check-circle"></i>
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-4">
              ¡Navegación Funcionando!
            </h3>
            <p className="text-gray-600 mb-4">
              La navegación a la página de informe completo está funcionando correctamente.
            </p>
            <div className="bg-gray-100 p-4 rounded-lg mb-6">
              <p className="text-sm text-gray-700">
                <strong>ID del Paciente:</strong> {patientId}
              </p>
              <p className="text-sm text-gray-700">
                <strong>Ruta actual:</strong> /admin/informe-completo/{patientId}
              </p>
            </div>
            <div className="space-x-4">
              <Button 
                onClick={() => navigate('/admin/reports')}
                variant="outline"
              >
                <i className="fas fa-arrow-left mr-2"></i>
                Volver a Resultados
              </Button>
              <Button 
                onClick={() => navigate(`/admin/informe-completo/${patientId}`)}
                variant="primary"
              >
                <i className="fas fa-file-alt mr-2"></i>
                Ir al Informe Real
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default TestNavigation;

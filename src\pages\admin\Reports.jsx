import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import supabase from '../../api/supabaseClient';
import { toast } from 'react-toastify';

const Reports = () => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAptitude, setSelectedAptitude] = useState('');
  const [aptitudes, setAptitudes] = useState([]);

  // Cargar datos al montar el componente
  useEffect(() => {
    fetchResults();
    fetchAptitudes();
  }, []);

  // Función para obtener todas las aptitudes
  const fetchAptitudes = async () => {
    try {
      const { data, error } = await supabase
        .from('aptitudes')
        .select('*')
        .order('codigo');

      if (error) throw error;
      setAptitudes(data || []);
    } catch (error) {
      console.error('Error al cargar aptitudes:', error);
      toast.error('Error al cargar las aptitudes');
    }
  };

  // Función para obtener todos los resultados con información de pacientes y aptitudes
  const fetchResults = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('resultados')
        .select(`
          *,
          pacientes:paciente_id (
            id,
            nombre,
            apellido,
            documento,
            email,
            genero,
            fecha_nacimiento
          ),
          aptitudes:aptitud_id (
            codigo,
            nombre,
            descripcion
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setResults(data || []);
    } catch (error) {
      console.error('Error al cargar resultados:', error);
      toast.error('Error al cargar los resultados');
    } finally {
      setLoading(false);
    }
  };

  // Filtrar resultados según búsqueda y aptitud seleccionada
  const filteredResults = results.filter(result => {
    const matchesSearch = !searchTerm ||
      (result.pacientes?.nombre && result.pacientes.nombre.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (result.pacientes?.apellido && result.pacientes.apellido.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (result.pacientes?.documento && result.pacientes.documento.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesAptitude = !selectedAptitude || result.aptitudes?.codigo === selectedAptitude;

    return matchesSearch && matchesAptitude;
  });

  // Función para formatear la fecha
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Función para formatear el tiempo en minutos y segundos
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Función para obtener el color del badge según el puntaje
  const getScoreBadgeColor = (score, maxScore) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 80) return 'bg-green-100 text-green-800';
    if (percentage >= 60) return 'bg-yellow-100 text-yellow-800';
    if (percentage >= 40) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Encabezado */}
      <div className="mb-8">
        <div className="flex items-center justify-center mb-4">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full mr-3 shadow-lg">
            <i className="fas fa-chart-bar text-white text-lg"></i>
          </div>
          <h1 className="text-4xl font-bold text-gray-900">
            Resultados de Tests
          </h1>
        </div>
        <p className="text-lg text-gray-600 text-center">
          Visualiza todos los resultados de tests aplicados a los pacientes
        </p>
        <div className="mt-4 w-24 h-1 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full mx-auto"></div>
      </div>

      {/* Filtros */}
      <Card className="mb-6">
        <CardHeader>
          <h2 className="text-lg font-semibold text-gray-800">
            <i className="fas fa-filter mr-2 text-blue-600"></i>
            Filtros de Búsqueda
          </h2>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Búsqueda por paciente */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Buscar Paciente
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Nombre, apellido o documento..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              </div>
            </div>

            {/* Filtro por aptitud */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Filtrar por Test
              </label>
              <select
                value={selectedAptitude}
                onChange={(e) => setSelectedAptitude(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Todos los tests</option>
                {aptitudes.map((aptitude) => (
                  <option key={aptitude.id} value={aptitude.codigo}>
                    {aptitude.codigo} - {aptitude.nombre}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Estadísticas Generales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardBody className="text-center">
            <div className="text-2xl font-bold text-blue-600">{results.length}</div>
            <div className="text-sm text-gray-600">Total Resultados</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {[...new Set(results.map(r => r.paciente_id))].length}
            </div>
            <div className="text-sm text-gray-600">Pacientes Evaluados</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {aptitudes.length}
            </div>
            <div className="text-sm text-gray-600">Tests Disponibles</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {results.length > 0 ? Math.round(results.reduce((sum, r) => sum + (r.puntaje_directo || 0), 0) / results.length) : 0}
            </div>
            <div className="text-sm text-gray-600">Promedio PD</div>
          </CardBody>
        </Card>
      </div>

      {/* Tabla de Resultados */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold text-gray-800">
              <i className="fas fa-table mr-2 text-green-600"></i>
              Resultados Detallados
            </h2>
            <div className="text-sm text-gray-600">
              {filteredResults.length} de {results.length} resultados
            </div>
          </div>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="text-center py-12">
              <div className="inline-flex items-center">
                <i className="fas fa-spinner fa-spin mr-2 text-blue-600"></i>
                <span className="text-gray-600">Cargando resultados...</span>
              </div>
            </div>
          ) : filteredResults.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Paciente
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Test
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Puntaje PD
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Errores
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tiempo
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Concentración
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Fecha
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredResults.map((result) => (
                    <tr key={result.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                              <span className="text-white font-medium text-sm">
                                {result.pacientes?.genero === 'masculino' ? (
                                  <i className="fas fa-mars text-blue-200"></i>
                                ) : result.pacientes?.genero === 'femenino' ? (
                                  <i className="fas fa-venus text-pink-200"></i>
                                ) : (
                                  <i className="fas fa-user text-gray-200"></i>
                                )}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {result.pacientes?.nombre} {result.pacientes?.apellido}
                            </div>
                            <div className="text-sm text-gray-500">
                              {result.pacientes?.documento}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            result.aptitudes?.codigo === 'V' ? 'bg-blue-100 text-blue-800' :
                            result.aptitudes?.codigo === 'E' ? 'bg-indigo-100 text-indigo-800' :
                            result.aptitudes?.codigo === 'A' ? 'bg-red-100 text-red-800' :
                            result.aptitudes?.codigo === 'R' ? 'bg-amber-100 text-amber-800' :
                            result.aptitudes?.codigo === 'N' ? 'bg-teal-100 text-teal-800' :
                            result.aptitudes?.codigo === 'M' ? 'bg-slate-100 text-slate-800' :
                            result.aptitudes?.codigo === 'O' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {result.aptitudes?.codigo}
                          </span>
                          <div className="ml-2">
                            <div className="text-sm font-medium text-gray-900">
                              {result.aptitudes?.nombre}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium ${
                          getScoreBadgeColor(result.puntaje_directo,
                            result.aptitudes?.codigo === 'V' ? 32 :
                            result.aptitudes?.codigo === 'E' ? 28 :
                            result.aptitudes?.codigo === 'A' ? 80 :
                            result.aptitudes?.codigo === 'R' ? 32 :
                            result.aptitudes?.codigo === 'N' ? 30 :
                            result.aptitudes?.codigo === 'M' ? 28 :
                            result.aptitudes?.codigo === 'O' ? 32 : 100
                          )
                        }`}>
                          {result.puntaje_directo || 0}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {result.errores || 0}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatTime(result.tiempo_segundos || 0)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {result.concentracion ? `${parseFloat(result.concentracion).toFixed(1)}%` : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(result.created_at)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="flex flex-col items-center">
                <i className="fas fa-search text-4xl text-gray-300 mb-4"></i>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron resultados</h3>
                <p className="text-gray-500">
                  {searchTerm || selectedAptitude
                    ? 'Intenta ajustar los filtros de búsqueda'
                    : 'Aún no hay resultados de tests registrados en el sistema'
                  }
                </p>
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default Reports;
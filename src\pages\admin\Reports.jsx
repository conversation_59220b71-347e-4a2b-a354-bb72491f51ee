import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody } from '../../components/ui/Card';
import supabase from '../../api/supabaseClient';
import { toast } from 'react-toastify';
import { convertirPdAPC } from '../../utils/baremosUtils';

const Reports = () => {
  const [resultsByPatient, setResultsByPatient] = useState({});
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAptitude, setSelectedAptitude] = useState('');
  const [aptitudes, setAptitudes] = useState([]);
  const [allResults, setAllResults] = useState([]); // Para estadísticas generales

  // Cargar datos al montar el componente
  useEffect(() => {
    fetchResultsAndGroup();
    fetchAptitudes();
  }, []);

  // Función para obtener todas las aptitudes
  const fetchAptitudes = async () => {
    try {
      const { data, error } = await supabase
        .from('aptitudes')
        .select('*')
        .order('codigo');

      if (error) throw error;
      setAptitudes(data || []);
    } catch (error) {
      console.error('Error al cargar aptitudes:', error);
      toast.error('Error al cargar las aptitudes');
    }
  };

  // Función para obtener todos los resultados, agruparlos por paciente y calcular PC
  const fetchResultsAndGroup = async () => { // Renombrada y modificada
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('resultados')
        .select(`
          *,
          pacientes:paciente_id (
            id,
            nombre,
            apellido,
            documento,
            email,
            genero,
            fecha_nacimiento
          ),
          aptitudes:aptitud_id (
            codigo,
            nombre,
            descripcion
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAllResults(data || []); // Guardar todos los resultados para estadísticas

      // Agrupar resultados por paciente y calcular PC
      const grouped = (data || []).reduce((acc, result) => {
        const patientId = result.pacientes?.id;
        if (!patientId) return acc;

        if (!acc[patientId]) {
          acc[patientId] = {
            patientInfo: result.pacientes,
            evaluations: [],
            generalEvaluationDate: result.created_at // Tomar la fecha de la primera evaluación encontrada
          };
        }

        // Calcular edad del paciente para obtener PC
        const age = result.pacientes?.fecha_nacimiento ? calculateAge(result.pacientes.fecha_nacimiento) : null;
        const pcScore = age && result.puntaje_directo !== null && result.aptitudes?.codigo
          ? convertirPdAPC(result.puntaje_directo, result.aptitudes.codigo, age)
          : null;

        acc[patientId].evaluations.push({ ...result, pc_score: pcScore });
        // Actualizar la fecha general si se encuentra una más reciente (o mantener la primera)
        if (new Date(result.created_at) > new Date(acc[patientId].generalEvaluationDate)) {
            // acc[patientId].generalEvaluationDate = result.created_at; // Opcional: usar la más reciente
        }
        return acc;
      }, {});
      setResultsByPatient(grouped);
    } catch (error) {
      console.error('Error al cargar resultados:', error);
      toast.error('Error al cargar los resultados');
    } finally {
      setLoading(false);
    }
  };

  // Filtrar pacientes y sus resultados
  const filteredPatients = Object.values(resultsByPatient).filter(patientData => {
    const patientInfo = patientData.patientInfo;
    const matchesSearch = !searchTerm ||
      (patientInfo?.nombre && patientInfo.nombre.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (patientInfo?.apellido && patientInfo.apellido.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (patientInfo?.documento && patientInfo.documento.toLowerCase().includes(searchTerm.toLowerCase()));

    if (!matchesSearch) return false;

    // Si hay un filtro de aptitud, verificar si el paciente tiene al menos un resultado para esa aptitud
    if (selectedAptitude) {
      return patientData.evaluations.some(result => result.aptitudes?.codigo === selectedAptitude);
    }
    return true; // Si no hay filtro de aptitud, incluir al paciente si coincide con la búsqueda
  });

  // Función para calcular la edad
  const calculateAge = (fechaNacimiento) => {
    if (!fechaNacimiento) return null;

    const hoy = new Date();
    const nacimiento = new Date(fechaNacimiento);
    let edad = hoy.getFullYear() - nacimiento.getFullYear();
    const mes = hoy.getMonth() - nacimiento.getMonth();

    if (mes < 0 || (mes === 0 && hoy.getDate() < nacimiento.getDate())) {
      edad--;
    }

    return edad;
  };

  // Función para filtrar las evaluaciones de un paciente específico por aptitud seleccionada
  const getFilteredEvaluationsForPatient = (evaluations) => {
    if (!selectedAptitude) return evaluations;
    return evaluations.filter(result => result.aptitudes?.codigo === selectedAptitude);
  };

  // Función para formatear la fecha
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Función para formatear el tiempo en minutos y segundos
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Función para obtener el color del badge según el puntaje
  const getScoreBadgeColor = (score, maxScore) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 80) return 'bg-green-100 text-green-800';
    if (percentage >= 60) return 'bg-yellow-100 text-yellow-800';
    if (percentage >= 40) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Encabezado */}
      <div className="mb-8">
        <div className="flex items-center justify-center mb-4">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full mr-3 shadow-lg">
            <i className="fas fa-chart-bar text-white text-lg"></i>
          </div>
          <h1 className="text-4xl font-bold text-gray-900">
            Resultados de Tests
          </h1>
        </div>
        <p className="text-lg text-gray-600 text-center">
          Visualiza todos los resultados de tests aplicados a los pacientes
        </p>
        <div className="mt-4 w-24 h-1 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full mx-auto"></div>
      </div>

      {/* Filtros */}
      <Card className="mb-6">
        <CardHeader>
          <h2 className="text-lg font-semibold text-gray-800">
            <i className="fas fa-filter mr-2 text-blue-600"></i>
            Filtros de Búsqueda
          </h2>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Búsqueda por paciente */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Buscar Paciente
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Nombre, apellido o documento..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              </div>
            </div>

            {/* Filtro por aptitud */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Filtrar por Test
              </label>
              <select
                value={selectedAptitude}
                onChange={(e) => setSelectedAptitude(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Todos los tests</option>
                {aptitudes.map((aptitude) => (
                  <option key={aptitude.id} value={aptitude.codigo}>
                    {aptitude.codigo} - {aptitude.nombre}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Estadísticas Generales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardBody className="text-center">
            <div className="text-2xl font-bold text-blue-600">{allResults.length}</div>
            <div className="text-sm text-gray-600">Total Resultados</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {Object.keys(resultsByPatient).length} 
            </div>
            <div className="text-sm text-gray-600">Pacientes Evaluados</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {aptitudes.length}
            </div>
            <div className="text-sm text-gray-600">Tests Disponibles</div>
          </CardBody>
        </Card>
        <Card>
          <CardBody className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {allResults.length > 0 ? Math.round(allResults.reduce((sum, r) => sum + (r.puntaje_directo || 0), 0) / allResults.length) : 0}
            </div>
            <div className="text-sm text-gray-600">Promedio PD</div>
          </CardBody>
        </Card>
      </div>

      {/* Resultados por Paciente */} {/* Modificado el título */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold text-gray-800">
              <i className="fas fa-table mr-2 text-green-600"></i>
              Resultados por Paciente
            </h2>
            <div className="text-sm text-gray-600">
              Mostrando {filteredPatients.length} de {Object.keys(resultsByPatient).length} pacientes
            </div>
          </div>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="text-center py-12">
              <div className="inline-flex items-center">
                <i className="fas fa-spinner fa-spin mr-2 text-blue-600"></i>
                <span className="text-gray-600">Cargando resultados...</span>
              </div>
            </div>
          ) : filteredPatients.length > 0 ? (
            filteredPatients.map(({ patientInfo, evaluations, generalEvaluationDate }) => {
              const patientEvaluations = getFilteredEvaluationsForPatient(evaluations);
              if (patientEvaluations.length === 0 && selectedAptitude) return null; // No mostrar paciente si no tiene resultados para el filtro de aptitud

              return (
                <div key={patientInfo.id} className="mb-8 p-4 border border-gray-200 rounded-lg shadow-sm">
                  {/* Encabezado del Paciente */}
                  <div className="mb-4 pb-2 border-b border-gray-200">
                    <div className="flex items-center">
                        <div className="flex-shrink-0 h-12 w-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center mr-3">
                            <span className="text-white font-medium text-lg">
                            {patientInfo?.genero === 'masculino' ? (
                                <i className="fas fa-mars text-blue-100"></i>
                            ) : patientInfo?.genero === 'femenino' ? (
                                <i className="fas fa-venus text-pink-100"></i>
                            ) : (
                                <i className="fas fa-user text-gray-100"></i>
                            )}
                            </span>
                        </div>
                        <div>
                            <h3 className="text-xl font-semibold text-gray-800">
                                {patientInfo.nombre} {patientInfo.apellido}
                            </h3>
                            <p className="text-sm text-gray-500">ID: {patientInfo.documento}</p>
                            <p className="text-sm text-gray-500">Fecha Evaluación General: {formatDate(generalEvaluationDate)}</p>
                        </div>
                    </div>
                  </div>

                  {/* Tabla de Resultados del Paciente */}
                  {patientEvaluations.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Test
                            </th>
                            <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Puntaje PD
                            </th>
                            <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Puntaje PC
                            </th>
                            <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Errores
                            </th>
                            <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Tiempo
                            </th>
                            <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Fecha Test
                            </th>
                            <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Acciones
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {patientEvaluations.map((result) => (
                            <tr key={result.id} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap text-center">
                                <div className="flex items-center justify-center">
                                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            result.aptitudes?.codigo === 'V' ? 'bg-blue-100 text-blue-800' :
                            result.aptitudes?.codigo === 'E' ? 'bg-indigo-100 text-indigo-800' :
                            result.aptitudes?.codigo === 'A' ? 'bg-red-100 text-red-800' :
                            result.aptitudes?.codigo === 'R' ? 'bg-amber-100 text-amber-800' :
                            result.aptitudes?.codigo === 'N' ? 'bg-teal-100 text-teal-800' :
                            result.aptitudes?.codigo === 'M' ? 'bg-slate-100 text-slate-800' :
                                    result.aptitudes?.codigo === 'O' ? 'bg-green-100 text-green-800' :
                                    'bg-gray-100 text-gray-800'
                                  }`}>
                                    {result.aptitudes?.codigo}
                                  </span>
                                  <div className="ml-2 text-sm font-medium text-gray-900">
                                    {result.aptitudes?.nombre}
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-center">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium ${
                          getScoreBadgeColor(result.puntaje_directo,
                            result.aptitudes?.codigo === 'V' ? 32 :
                            result.aptitudes?.codigo === 'E' ? 28 :
                            result.aptitudes?.codigo === 'A' ? 80 :
                            result.aptitudes?.codigo === 'R' ? 32 :
                            result.aptitudes?.codigo === 'N' ? 30 :
                            result.aptitudes?.codigo === 'M' ? 28 :
                                    result.aptitudes?.codigo === 'O' ? 32 : 100
                                  )
                                }`}>
                                  {result.puntaje_directo || 0}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium ${result.pc_score ? getScoreBadgeColor(result.pc_score, 100) : 'bg-gray-100 text-gray-800'}`}>
                                  {result.pc_score !== null ? result.pc_score : 'N/A'}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                                {result.errores || 0}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                                {formatTime(result.tiempo_segundos || 0)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                {formatDate(result.created_at)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-center">
                                <a href={`/student/informe/${result.pacientes?.id}`} className="text-indigo-600 hover:text-indigo-900">
                                  Ver Informe
                                </a>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-gray-500">No hay resultados de tests para este paciente que coincidan con los filtros.</p>
                    </div>
                  )}
                </div>
              );
            })
          ) : (
            <div className="text-center py-12">
              <div className="flex flex-col items-center">
                <i className="fas fa-search text-4xl text-gray-300 mb-4"></i>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron resultados</h3>
                <p className="text-gray-500">
                  {searchTerm || selectedAptitude
                    ? 'Intenta ajustar los filtros de búsqueda'
                    : 'Aún no hay resultados de tests registrados en el sistema'
                  }
                </p>
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default Reports;
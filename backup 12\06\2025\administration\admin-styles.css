/* Estilos específicos para la página de administración */

/* Estos estilos ayudan a detectar problemas de renderizado */
.administration-container {
  animation: fadeIn 0.3s ease-in-out;
}

/* Asegura que las pestañas tengan el estilo correcto */
.tab-container {
  min-height: 400px;
}

/* Animación de entrada para hacer más visible cuando la página se carga */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Eliminar posibles estilos conflictivos */
.tab-panel {
  position: relative !important;
  overflow: visible !important;
}

/* Estilos de depuración para ayudar a identificar problemas */
.debug-outline {
  outline: 2px dashed red;
}

/* Corrige posibles problemas con modales */
#modal-root {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  pointer-events: none;
}

#modal-root > * {
  pointer-events: auto;
}

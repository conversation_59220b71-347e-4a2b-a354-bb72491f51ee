import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import supabase from '../../api/supabaseClient';
import { toast } from 'react-toastify';
import TestCard from './components/TestCard';

const Questionnaire = () => {
  const [patients, setPatients] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState([]);
  const [loadingResults, setLoadingResults] = useState(false);

  // Cargar pacientes al montar el componente
  useEffect(() => {
    fetchPatients();
  }, []);

  // Cargar resultados cuando se selecciona un paciente
  useEffect(() => {
    if (selectedPatient) {
      fetchPatientResults(selectedPatient.id);
    }
  }, [selectedPatient]);

  // Función para obtener pacientes de Supabase
  const fetchPatients = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('pacientes')
        .select(`
          id,
          nombre,
          apellido,
          documento,
          email,
          telefono,
          genero,
          fecha_nacimiento
        `)
        .order('nombre', { ascending: true });

      if (error) throw error;
      setPatients(data || []);
    } catch (error) {
      console.error('Error al cargar pacientes:', error.message);
      toast.error('Error al cargar la lista de pacientes');
    } finally {
      setLoading(false);
    }
  };

  // Función para obtener resultados del paciente
  const fetchPatientResults = async (patientId) => {
    try {
      setLoadingResults(true);
      const { data, error } = await supabase
        .from('resultados')
        .select(`
          *,
          aptitudes:aptitud_id (
            codigo,
            nombre,
            descripcion
          )
        `)
        .eq('paciente_id', patientId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setResults(data || []);
    } catch (error) {
      console.error('Error al cargar resultados:', error.message);
      toast.error('Error al cargar los resultados del paciente');
    } finally {
      setLoadingResults(false);
    }
  };

  // Filtrar pacientes según el término de búsqueda
  const filteredPatients = patients.filter(patient =>
    patient.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.apellido.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (patient.documento && patient.documento.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Función para seleccionar un paciente
  const handleSelectPatient = (patient) => {
    setSelectedPatient(patient);
    setSearchTerm(`${patient.nombre} ${patient.apellido}`);
  };

  // Función para limpiar la selección
  const handleClearSelection = () => {
    setSelectedPatient(null);
    setSearchTerm('');
    setResults([]);
  };

  // Calcular concentración
  const calculateConcentration = (atencionResult, errores) => {
    if (!atencionResult || atencionResult === 0) return 0;
    return ((atencionResult / (atencionResult + errores)) * 100).toFixed(2);
  };

  // Obtener resultado por código de aptitud
  const getResultByCode = (code) => {
    return results.find(result => result.aptitudes?.codigo === code);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Encabezado */}
      <div className="mb-8 text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mr-3 shadow-lg">
            <i className="fas fa-clipboard-list text-white text-lg"></i>
          </div>
          <h1 className="text-4xl font-bold text-gray-900">
            Cuestionario de Evaluación
          </h1>
        </div>
        <p className="text-lg text-gray-600">
          Selecciona un paciente para ver sus resultados y aplicar nuevos tests
        </p>
        <div className="mt-4 w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto"></div>
      </div>

      {/* Búsqueda de Paciente */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">
          <i className="fas fa-search mr-2 text-blue-600"></i>
          Buscar Paciente
        </h2>
        
        <div className="relative">
          <input
            type="text"
            placeholder="Buscar por nombre, apellido o documento..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          
          {selectedPatient && (
            <button
              onClick={handleClearSelection}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <i className="fas fa-times"></i>
            </button>
          )}
        </div>

        {/* Lista de pacientes filtrados */}
        {searchTerm && !selectedPatient && (
          <div className="mt-4 max-h-60 overflow-y-auto border border-gray-200 rounded-lg">
            {loading ? (
              <div className="p-4 text-center">
                <i className="fas fa-spinner fa-spin mr-2"></i>
                Cargando pacientes...
              </div>
            ) : filteredPatients.length > 0 ? (
              filteredPatients.map((patient) => (
                <div
                  key={patient.id}
                  onClick={() => handleSelectPatient(patient)}
                  className="p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">
                        {patient.nombre} {patient.apellido}
                      </p>
                      <p className="text-sm text-gray-500">
                        {patient.documento && `Doc: ${patient.documento}`}
                        {patient.email && ` • ${patient.email}`}
                      </p>
                    </div>
                    <i className="fas fa-chevron-right text-gray-400"></i>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-4 text-center text-gray-500">
                No se encontraron pacientes que coincidan con la búsqueda
              </div>
            )}
          </div>
        )}
      </div>

      {/* Información del Paciente Seleccionado */}
      {selectedPatient && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            <i className="fas fa-user mr-2 text-green-600"></i>
            Paciente Seleccionado
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Nombre Completo</label>
              <p className="text-gray-900">{selectedPatient.nombre} {selectedPatient.apellido}</p>
            </div>
            {selectedPatient.documento && (
              <div>
                <label className="text-sm font-medium text-gray-500">Documento</label>
                <p className="text-gray-900">{selectedPatient.documento}</p>
              </div>
            )}
            {selectedPatient.email && (
              <div>
                <label className="text-sm font-medium text-gray-500">Email</label>
                <p className="text-gray-900">{selectedPatient.email}</p>
              </div>
            )}
            {selectedPatient.genero && (
              <div>
                <label className="text-sm font-medium text-gray-500">Género</label>
                <p className="text-gray-900 capitalize">{selectedPatient.genero}</p>
              </div>
            )}
            {selectedPatient.fecha_nacimiento && (
              <div>
                <label className="text-sm font-medium text-gray-500">Fecha de Nacimiento</label>
                <p className="text-gray-900">{new Date(selectedPatient.fecha_nacimiento).toLocaleDateString()}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Resultados del Paciente */}
      {selectedPatient && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            <i className="fas fa-chart-bar mr-2 text-purple-600"></i>
            Resultados de Tests Aplicados
          </h2>
          
          {loadingResults ? (
            <div className="text-center py-8">
              <i className="fas fa-spinner fa-spin mr-2"></i>
              Cargando resultados...
            </div>
          ) : results.length > 0 ? (
            <div className="space-y-4">
              {/* Tabla de resultados */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Test
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Puntaje Directo (PD)
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Percentil (PC)
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Concentración
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Fecha
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {results.map((result) => (
                      <tr key={result.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="text-sm font-medium text-gray-900">
                              {result.aptitudes?.nombre || 'N/A'}
                            </div>
                            <div className="text-sm text-gray-500 ml-2">
                              ({result.aptitudes?.codigo || 'N/A'})
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {result.puntaje_directo || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {result.percentil || 'Pendiente'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {result.concentracion ? `${result.concentracion}%` : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(result.created_at).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <i className="fas fa-clipboard-check text-4xl mb-4 text-gray-300"></i>
              <p>Este paciente no tiene resultados de tests registrados</p>
              <p className="text-sm">Aplica tests usando las opciones de abajo</p>
            </div>
          )}
        </div>
      )}

      {/* Tests Disponibles */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          <i className="fas fa-clipboard-list mr-2 text-blue-600"></i>
          Tests Disponibles
        </h2>

        {!selectedPatient && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <i className="fas fa-info-circle text-yellow-600 mr-2"></i>
              <p className="text-yellow-800">
                Selecciona un paciente para poder aplicar los tests y guardar los resultados
              </p>
            </div>
          </div>
        )}

        {/* Grid de tarjetas de tests */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 auto-rows-fr">
          {/* Aptitud Verbal */}
          <TestCard
            test={{
              id: 'verbal',
              title: 'Aptitud Verbal',
              description: 'Evaluación de analogías verbales y comprensión de relaciones entre conceptos',
              time: 12,
              questions: 32
            }}
            iconClass="fas fa-comments"
            bgClass="bg-blue-100"
            textClass="text-blue-600"
            buttonColor="blue"
            abbreviation="V"
            showButton={!!selectedPatient}
            disabled={!selectedPatient}
            patientId={selectedPatient?.id}
          />

          {/* Aptitud Espacial */}
          <TestCard
            test={{
              id: 'espacial',
              title: 'Aptitud Espacial',
              description: 'Razonamiento espacial con cubos y redes',
              time: 15,
              questions: 28
            }}
            iconClass="fas fa-cube"
            bgClass="bg-indigo-100"
            textClass="text-indigo-600"
            buttonColor="indigo"
            abbreviation="E"
            showButton={!!selectedPatient}
            disabled={!selectedPatient}
            patientId={selectedPatient?.id}
          />

          {/* Test de Atención */}
          <TestCard
            test={{
              id: 'atencion',
              title: 'Atención',
              description: 'Rapidez y precisión en la localización de símbolos',
              time: 8,
              questions: 80
            }}
            iconClass="fas fa-eye"
            bgClass="bg-red-100"
            textClass="text-red-600"
            buttonColor="red"
            abbreviation="A"
            showButton={!!selectedPatient}
            disabled={!selectedPatient}
            patientId={selectedPatient?.id}
          />

          {/* Razonamiento */}
          <TestCard
            test={{
              id: 'razonamiento',
              title: 'Razonamiento',
              description: 'Continuar series lógicas de figuras',
              time: 20,
              questions: 32
            }}
            iconClass="fas fa-puzzle-piece"
            bgClass="bg-amber-100"
            textClass="text-amber-600"
            buttonColor="amber"
            abbreviation="R"
            showButton={!!selectedPatient}
            disabled={!selectedPatient}
            patientId={selectedPatient?.id}
          />

          {/* Aptitud Numérica */}
          <TestCard
            test={{
              id: 'numerico',
              title: 'Aptitud Numérica',
              description: 'Resolución de igualdades, series numéricas y análisis de tablas de datos',
              time: 20,
              questions: 30
            }}
            iconClass="fas fa-calculator"
            bgClass="bg-teal-100"
            textClass="text-teal-600"
            buttonColor="teal"
            abbreviation="N"
            showButton={!!selectedPatient}
            disabled={!selectedPatient}
            patientId={selectedPatient?.id}
          />

          {/* Aptitud Mecánica */}
          <TestCard
            test={{
              id: 'mecanico',
              title: 'Aptitud Mecánica',
              description: 'Comprensión de principios físicos y mecánicos básicos',
              time: 12,
              questions: 28
            }}
            iconClass="fas fa-cogs"
            bgClass="bg-slate-100"
            textClass="text-slate-600"
            buttonColor="slate"
            abbreviation="M"
            showButton={!!selectedPatient}
            disabled={!selectedPatient}
            patientId={selectedPatient?.id}
          />

          {/* Ortografía */}
          <TestCard
            test={{
              id: 'ortografia',
              title: 'Ortografía',
              description: 'Identificación de palabras con errores ortográficos',
              time: 10,
              questions: 32
            }}
            iconClass="fas fa-spell-check"
            bgClass="bg-green-100"
            textClass="text-green-600"
            buttonColor="green"
            abbreviation="O"
            showButton={!!selectedPatient}
            disabled={!selectedPatient}
            patientId={selectedPatient?.id}
          />
        </div>
      </div>
    </div>
  );
};

export default Questionnaire;

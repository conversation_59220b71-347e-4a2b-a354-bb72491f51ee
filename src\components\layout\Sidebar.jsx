import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Sidebar = ({ isOpen, toggleSidebar }) => {
  const location = useLocation();

  // Inicializar favoritos desde localStorage o usar valores predeterminados
  const [favorites, setFavorites] = useState(() => {
    const savedFavorites = localStorage.getItem('sidebarFavorites');
    return savedFavorites ? JSON.parse(savedFavorites) : {
      dashboard: false,
      home: false,
      patients: false,
      tests: false,
      results: false,
      settings: false,
      help: false
    };
  });

  // Guardar favoritos en localStorage cuando cambien
  useEffect(() => {
    localStorage.setItem('sidebarFavorites', JSON.stringify(favorites));
  }, [favorites]);

  // Toggle para favoritos
  const toggleFavorite = (key, e) => {
    e.preventDefault();
    e.stopPropagation();
    setFavorites(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Verificar si una ruta está activa
  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/';
    }

    return location.pathname === path ||
           (location.pathname.startsWith(path) &&
            (location.pathname.length === path.length ||
             location.pathname[path.length] === '/'));
  };

  // Elementos del menú
  const menuItems = [
    { name: 'Dashboard', path: '/admin/administration', icon: 'chart-line', key: 'dashboard' },
    { name: 'Inicio', path: '/home', icon: 'home', key: 'home' },
    { name: 'Pacientes', path: '/student/patients', icon: 'users', key: 'patients' },
    { name: 'Cuestionario', path: '/student/questionnaire', icon: 'clipboard-list', key: 'tests' },
    { name: 'Resultados', path: '/student/resultados', icon: 'chart-bar', key: 'results' },
    { name: 'Informes Guardados', path: '/student/informes-guardados', icon: 'archive', key: 'saved-reports' },
    { name: 'Admin - Resultados', path: '/admin/reports', icon: 'chart-line', key: 'admin-results' },
    { name: 'Admin - Informes', path: '/admin/informes-guardados', icon: 'folder-open', key: 'admin-saved-reports' },
    { name: 'Configuración', path: '/settings', icon: 'sliders-h', key: 'settings' },
    { name: 'Ayuda', path: '/help', icon: 'question-circle', key: 'help' }
  ];

  // Filtrar favoritos
  const favoriteItems = menuItems.filter(item => favorites[item.key]);

  return (
    <div className={`sidebar bg-[#121940] text-[#a4b1cd] fixed top-0 left-0 h-full z-50 transition-all duration-300 ease-in-out
                     ${isOpen ? 'w-64' : 'w-[70px]'}`}>
      <div className="sidebar-header p-5 flex justify-between items-center border-b border-opacity-10 border-white">
        {isOpen && (
          <h1 className="text-xl font-bold text-white">
            Activatu<span className="text-[#ffda0a]">mente</span>
          </h1>
        )}
        <button
          onClick={toggleSidebar}
          className="text-[#a4b1cd] cursor-pointer"
        >
          <i className={`fas ${isOpen ? 'fa-chevron-left' : 'fa-chevron-right'}`}></i>
        </button>
      </div>

      {/* Sección de favoritos */}
      {favoriteItems.length > 0 && (
        <div className="sidebar-section py-4 border-b border-opacity-10 border-white">
          {isOpen && (
            <h2 className="uppercase text-xs px-5 mb-3 tracking-wider font-semibold text-gray-400 flex items-center">
              <i className="fas fa-star text-[#ffda0a] mr-2"></i>
              Favoritos
            </h2>
          )}
          <ul className="menu-list">
            {favoriteItems.map((item) => (
              <li
                key={`fav-${item.key}`}
                className={`p-3 px-5 hover:bg-opacity-5 hover:bg-white transition-all duration-200
                          ${isActive(item.path) ? 'bg-[#ffda0a] bg-opacity-10 text-white border-l-4 border-[#ffda0a]' : ''}`}
              >
                <div className="flex items-center justify-between w-full">
                  <Link
                    to={item.path}
                    className="flex items-center flex-grow"
                  >
                    <i className={`fas fa-${item.icon} ${!isOpen ? '' : 'mr-3'} w-5 text-center ${isActive(item.path) ? 'text-[#ffda0a]' : ''}`}></i>
                    {isOpen && <span>{item.name}</span>}
                  </Link>
                  {isOpen ? (
                    <span
                      className="text-[#ffda0a] cursor-pointer"
                      onClick={(e) => toggleFavorite(item.key, e)}
                      title="Quitar de favoritos"
                    >
                      <i className="fas fa-star"></i>
                    </span>
                  ) : (
                    <span className="text-[#ffda0a] text-xs absolute right-1 top-1">
                      <i className="fas fa-star"></i>
                    </span>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Menú principal */}
      <div className="sidebar-content py-4">
        {isOpen && favoriteItems.length > 0 && (
          <h2 className="uppercase text-xs px-5 mb-3 tracking-wider font-semibold text-gray-400 flex items-center">
            <i className="fas fa-list text-gray-400 mr-2"></i>
            Menú
          </h2>
        )}
        <ul className="menu-list">
          {menuItems.map((item) => (
            <li
              key={item.name}
              className={`p-3 px-5 hover:bg-opacity-5 hover:bg-white transition-all duration-200
                        ${isActive(item.path) ? 'bg-[#ffda0a] bg-opacity-10 text-white border-l-4 border-[#ffda0a]' : ''}`}
            >
              <div className="flex items-center justify-between w-full relative">
                <Link
                  to={item.path}
                  className="flex items-center flex-grow"
                >
                  <i className={`fas fa-${item.icon} ${!isOpen ? '' : 'mr-3'} w-5 text-center ${isActive(item.path) ? 'text-[#ffda0a]' : ''}`}></i>
                  {isOpen && <span>{item.name}</span>}
                </Link>
                {isOpen ? (
                  <span
                    className={`cursor-pointer hover:text-[#ffda0a] transition-colors duration-200 ${favorites[item.key] ? 'text-[#ffda0a]' : 'text-gray-500'}`}
                    onClick={(e) => toggleFavorite(item.key, e)}
                    title={favorites[item.key] ? "Quitar de favoritos" : "Añadir a favoritos"}
                  >
                    <i className={`${favorites[item.key] ? 'fas' : 'far'} fa-star`}></i>
                  </span>
                ) : (
                  favorites[item.key] && (
                    <span className="text-[#ffda0a] text-xs absolute right-1 top-1">
                      <i className="fas fa-star"></i>
                    </span>
                  )
                )}
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default Sidebar;
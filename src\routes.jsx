// src/routes.jsx
import { lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/layout/Layout';
import Loading from './components/common/Loading';
import ErrorBoundary from './components/common/ErrorBoundary';

// Importar el componente de carga mejorado
const PageLoader = lazy(() => import('./components/common/PageLoader'));

// Componente para envolver lazy components con ErrorBoundary
const LazyWithErrorBoundary = (Component) => (props) => (
  <ErrorBoundary>
    <Component {...props} />
  </ErrorBoundary>
);

// Componente para cargar páginas con un loader más atractivo
const LazyPage = (Component) => (props) => {
  return (
    <ErrorBoundary>
      <Suspense fallback={<PageLoader />}>
        <Component {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

// Lazy loading de componentes para optimizar rendimiento
const Home = LazyPage(lazy(() => import('./pages/home/<USER>')));
const Dashboard = LazyPage(lazy(() => import('./pages/dashboard/Dashboard')));
const Configuracion = LazyPage(lazy(() => import('./pages/Configuracion/Configuracion')));
const Help = LazyPage(lazy(() => import('./pages/help/Help')));

// Gestión de Pacientes
const Patients = LazyPage(lazy(() => import('./pages/student/Patients')));
const Tests = LazyPage(lazy(() => import('./pages/student/Tests')));
const Questionnaire = LazyPage(lazy(() => import('./pages/student/Questionnaire')));
const Results = LazyPage(lazy(() => import('./pages/student/Results')));
const Report = LazyPage(lazy(() => import('./pages/student/Report')));
const CompleteReport = LazyPage(lazy(() => import('./pages/student/CompleteReport')));
const SavedReports = LazyPage(lazy(() => import('./pages/student/SavedReports')));
const ViewSavedReport = LazyPage(lazy(() => import('./pages/student/ViewSavedReport')));
const InformeCualitativo = LazyPage(lazy(() => import('./pages/student/InformeCualitativo')));

// Rutas de administración
const Administration = LazyPage(lazy(() => import('./pages/admin/Administration')));
const Reports = LazyPage(lazy(() => import('./pages/admin/Reports')));
const AdminCompleteReport = LazyPage(lazy(() => import('./pages/admin/CompleteReport')));
const AdminSavedReports = LazyPage(lazy(() => import('./pages/admin/SavedReports')));
const AdminViewSavedReport = LazyPage(lazy(() => import('./pages/admin/ViewSavedReport')));

// Rutas de test
const TestInstructions = LazyPage(lazy(() => import('./pages/test/Instructions')));
const VerbalInstructions = LazyPage(lazy(() => import('./pages/test/verbalInstructions')));
const EspacialInstructions = LazyPage(lazy(() => import('./pages/test/espacialInstructions')));
const AtencionInstructions = LazyPage(lazy(() => import('./pages/test/atencionInstructions')));
const VerbalTest = LazyPage(lazy(() => import('./pages/test/Verbal')));
const EspacialTest = LazyPage(lazy(() => import('./pages/test/Espacial')));
const AtencionTest = LazyPage(lazy(() => import('./pages/test/Atencion')));
const RazonamientoTest = LazyPage(lazy(() => import('./pages/test/Razonamiento')));
const NumericoTest = LazyPage(lazy(() => import('./pages/test/Numerico')));
const MecanicoTest = LazyPage(lazy(() => import('./pages/test/Mecanico')));
const OrtografiaTest = LazyPage(lazy(() => import('./pages/test/Ortografia')));
const TestResults = LazyPage(lazy(() => import('./pages/test/Results')));

const AppRoutes = () => {
  return (
    <ErrorBoundary>
      <Suspense fallback={<Loading />}>
        <Routes>
          {/* Ruta principal - Redirige a home */}
          <Route path="/" element={<Navigate to="/home" replace />} />

          {/* Rutas principales con Layout */}
          <Route element={<Layout />}>
            {/* Dashboard */}
            <Route path="/admin/administration" element={<Administration />} />

            {/* Inicio */}
            <Route path="/home" element={<Home />} />

            {/* Pacientes */}
            <Route path="/student/patients" element={<Patients />} />

            {/* Tests */}
            <Route path="/student/tests" element={<Tests />} />

            {/* Cuestionario */}
            <Route path="/student/questionnaire" element={<Questionnaire />} />

            {/* Resultados */}
            <Route path="/student/resultados" element={<Results />} />
            <Route path="/student/results" element={<Results />} />
            <Route path="/student/informe/:id" element={<Report />} />
            <Route path="/student/informe-completo/:patientId" element={<CompleteReport />} />
            <Route path="/student/informes-guardados" element={<SavedReports />} />
            <Route path="/student/informe-guardado/:reportId" element={<ViewSavedReport />} />
            <Route path="/student/informe/:resultadoId" element={<InformeCualitativo />} />

            {/* Rutas de administrador */}
            <Route path="/admin/reports" element={<Reports />} />
            <Route path="/admin/informe-completo/:patientId" element={<AdminCompleteReport />} />
            <Route path="/admin/informes-guardados" element={<AdminSavedReports />} />
            <Route path="/admin/informe-guardado/:reportId" element={<AdminViewSavedReport />} />

            {/* Configuración */}
            <Route path="/settings" element={<Configuracion />} />

            {/* Ayuda */}
            <Route path="/help" element={<Help />} />

            {/* Rutas de test */}
            <Route path="/test">
              <Route index element={<Navigate to="/student/tests" replace />} />
              <Route path="instructions/:testId" element={<TestInstructions />} />
              <Route path="instructions/verbal" element={<VerbalInstructions />} />
              <Route path="instructions/espacial" element={<EspacialInstructions />} />
              <Route path="instructions/atencion" element={<AtencionInstructions />} />
              <Route path="verbal" element={<VerbalTest />} />
              <Route path="espacial" element={<EspacialTest />} />
              <Route path="atencion" element={<AtencionTest />} />
              <Route path="razonamiento" element={<RazonamientoTest />} />
              <Route path="numerico" element={<NumericoTest />} />
              <Route path="mecanico" element={<MecanicoTest />} />
              <Route path="ortografia" element={<OrtografiaTest />} />
              <Route path="results/:applicationId" element={<TestResults />} />
            </Route>
          </Route>

          {/* Ruta para manejar rutas no encontradas */}
          <Route path="*" element={<Navigate to="/home" replace />} />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
};

export default AppRoutes;